// 简单测试帐套显示功能
console.log('开始测试帐套显示功能...')

const bindings = [
  { templ_id: 1, relation_id: 101, company_id: 'company1', acc_model: 1 },
  { templ_id: 1, relation_id: 101, company_id: 'company1', acc_model: 2 },
  { templ_id: 1, relation_id: 101, company_id: 'company2', acc_model: 1 }
]

const companies = [
  { companyId: 'company1', companyName: '测试公司1', accModel: 1, appName: '税务帐套1' },
  { companyId: 'company1', companyName: '测试公司1', accModel: 2, appName: '股东帐套1' },
  { companyId: 'company2', companyName: '测试公司2', accModel: 1, appName: '税务帐套2' }
]

function getChildAccountSets(templateId, childId) {
  const filtered = bindings.filter(item => 
    item.templ_id === templateId && item.relation_id === childId
  )
  
  const result = filtered.map(binding => {
    const account = companies.find(tc => 
      tc.companyId === binding.company_id && tc.accModel === binding.acc_model
    )
    return account ? {
      companyName: account.companyName,
      accModel: account.accModel,
      appName: account.appName
    } : null
  }).filter(Boolean)
  
  return result
}

const result = getChildAccountSets(1, 101)
console.log('会计科目101关联的帐套:')
console.log(JSON.stringify(result, null, 2))
console.log(`关联数量: ${result.length}`)
console.log('测试完成!')
